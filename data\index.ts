export const navItems = [
  { name: "About", link: "#about" },
  { name: "Projects", link: "#projects" },
  { name: "Experiences", link: "#experiences" },
  { name: "Contact", link: "#contact" },
];

export const gridItems = [
  {
    id: 1,
    title: "Full Stack, Mobile & Desktop Developer",
    description: "",
    className: "lg:col-span-3 md:col-span-6 md:row-span-4 lg:min-h-[60vh]",
    imgClassName: "w-full h-full",
    titleClassName: "justify-end",
    img: "/b1.svg",
    spareImg: "",
  },
  {
    id: 2,
    title: "I build end-to-end digital solutions",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-2",
    imgClassName: "",
    titleClassName: "justify-start",
    img: "",
    spareImg: "",
  },
  {
    id: 3,
    title: "My tech stack",
    description: "I constantly try to improve",
    className: "lg:col-span-2 md:col-span-3 md:row-span-2",
    imgClassName: "",
    titleClassName: "justify-center",
    img: "",
    spareImg: "",
  },
  {
    id: 4,
    title: "Passionate about innovation & AI.",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-1",
    imgClassName: "",
    titleClassName: "justify-start",
    img: "/grid.svg",
    spareImg: "/b4.svg",
  },

  {
    id: 5,
    title: "Currently, I am learning Next.js.",
    description: "The Inside Scoop",
    className: "md:col-span-3 md:row-span-2",
    imgClassName: "absolute right-0 bottom-0 md:w-96 w-60",
    titleClassName: "justify-center md:justify-start lg:justify-center",
    img: "/b5.svg",
    spareImg: "/grid.svg",
  },
  {
    id: 6,
    title: "Do you want to start a project together?",
    description: "",
    className: "lg:col-span-2 md:col-span-3 md:row-span-1",
    imgClassName: "",
    titleClassName: "justify-center md:max-w-full max-w-60 text-center",
    img: "",
    spareImg: "",
  },
];

export const projects = [
  {
    id: 1,
    title: "Cotchini web app",
    des: "A web-based coaching platform developed as part of the PIDEV 3A course at Esprit School of Engineering. ",
    img: "/cow.png",
    iconLists: ["/symfony1.svg", "/twig.svg", "/mysql1.svg"],
    link: "https://github.com/labidi-houssem/PI_DEV_symfony",
  },
  {
    id: 2,
    title: "Cotchini desktop app",
    des: "A desktop coaching application developed as part of the PIDEV 3A course at Esprit School of Engineering. ",
    img: "/coj.png",
    iconLists: ["/java.svg", "/sb.png", "/mysql1.svg"],
    link: "https://github.com/labidi-houssem/PI_DEV",
  },
  {
    id: 2,
    title: "Designed and developed an IT asset management tool",
    des: "Centralized and consolidated all company asset information, enhancing visibility and coordination between departments. ",
    img: "/ttt.png",
    iconLists: ["/re.svg", "/nodejs.svg", "/express-js.svg", "/mongodb.svg"],
  },
  {
    id: 3,
    title: "CCNA 2-Exam-Simulation",
    des: "A free, interactive web-based practice exam for CCNA 2 SRWE (Switching, Routing, and Wireless Essentials) certification preparation.",
    img: "/ccna.png",
    iconLists: ["/html.svg", "/css.svg", "/js.svg"],
    link: "https://labidi-houssem.github.io/CCNA-Exam-Simulation/",
  },
  {
    id: 4,
    title: "musée virtuel",
    des: "A REAL Software-as-a-Service app with AI features and a payments and credits system using the latest tech stack.",
    img: "/muse.png",

    iconLists: ["/html.svg", "/css.svg", "/js.svg"],
    link: "https://labidi-houssem.github.io/mus-e-virtuel-/",
  },
  {
    id: 5,
    title: "Game 3D",
    des: "cat game 3d built with opengl and glut",
    img: "/cat.png",
    iconLists: ["/c++.svg", "/opengl.svg"],
    link: "https://github.com/labidi-houssem/cat-game-opengl-glut",
  },
  {
    id: 6,
    title: "desktop application",
    des: "Automated extraction of data from paper bills and resumes, converting scans to images, extracting text, and saving relevant data to CSV files. ",
    img: "/pyy.png",
    iconLists: [
      "/py.svg",
      "/qt.svg",
      "/mysql1.svg",
      "/pandas.svg",
      "/opencv.svg",
    ],
  
  },
  {
    id: 7,
    title: "moody Mobile application",
    des: "Created a mobile application for Healthcare",
    img: "/moody.png",
    iconLists: ["/flutter.svg", "/dart.svg", "/firebase.svg", "/sqlite.svg"],
    link: "https://github.com/labidi-houssem/moody",
  },
];

export const workExperience = [
  {
    id: 1,
    title: "Full Stack Developer Intern",
    desc: "Assisted in the development of a web-based platform using React.js and node.js and express.js and mongodb , enhancing interactivity.",
    className: "md:col-span-2",
    thumbnail: "/exp1.svg",
  },
  {
    id: 2,
    title: "Python Developer Intern",
    desc: "Designed and developed desktop application using python.",
    className: "md:col-span-2",
    thumbnail: "/exp2.svg",
  },
  {
    id: 3,
    title: "Freelance App Dev Project",
    desc: "Led the dev of a mobile app for a client, from initial concept to deployment .",
    className: "md:col-span-2", // change to md:col-span-2
    thumbnail: "/exp3.svg",
  },
  {
    id: 4,
    title: "Next.js Developer Intern",
    desc: "Developed and maintained user-facing features using modern frontend technologies.",
    className: "md:col-span-2",
    thumbnail: "/exp4.svg",
  },
];

export const socialMedia = [
  {
    id: 1,
    img: "/git.svg",
  },
  {
    id: 2,
    img: "/fb1.svg",
  },
  {
    id: 3,
    img: "/link.svg",
  },
];
